#!/bin/bash

# 9pfs日志添加脚本
# 在关键的9pfs初始化位置添加日志信息

echo "=== 9pfs日志添加脚本 ==="
echo "正在为9pfs关键组件添加日志信息..."

# 备份原始文件
backup_file() {
    local file=$1
    if [ -f "$file" ]; then
        cp "$file" "$file.backup"
        echo "已备份: $file -> $file.backup"
    else
        echo "警告: 文件不存在: $file"
        return 1
    fi
}

# 1. 修改网络协议层初始化日志
echo
echo "1. 修改网络协议层初始化 (net/9p/mod.c)..."
backup_file "common/net/9p/mod.c"

cat > /tmp/9p_mod_patch.c << 'EOF'
static int __init init_p9(void)
{
	int ret;

	pr_info("9P Protocol: Starting initialization\n");
	
	ret = p9_client_init();
	if (ret) {
		pr_err("9P Protocol: Client initialization failed: %d\n", ret);
		return ret;
	}

	p9_error_init();
	pr_info("Installing 9P2000 support\n");
	pr_info("9P Protocol: Core protocol support loaded successfully\n");
	
	p9_trans_fd_init();
	pr_info("9P Protocol: File descriptor transport initialized\n");

	return ret;
}
EOF

# 使用sed替换init_p9函数
sed -i '/^static int __init init_p9(void)$/,/^}$/c\
static int __init init_p9(void)\
{\
	int ret;\
\
	pr_info("9P Protocol: Starting initialization\\n");\
	\
	ret = p9_client_init();\
	if (ret) {\
		pr_err("9P Protocol: Client initialization failed: %d\\n", ret);\
		return ret;\
	}\
\
	p9_error_init();\
	pr_info("Installing 9P2000 support\\n");\
	pr_info("9P Protocol: Core protocol support loaded successfully\\n");\
	\
	p9_trans_fd_init();\
	pr_info("9P Protocol: File descriptor transport initialized\\n");\
\
	return ret;\
}' common/net/9p/mod.c

# 2. 修改文件系统层初始化日志
echo
echo "2. 修改文件系统层初始化 (fs/9p/v9fs.c)..."
backup_file "common/fs/9p/v9fs.c"

# 修改init_v9fs函数
sed -i '/^static int __init init_v9fs(void)$/,/return 0;$/{
    /pr_info("Installing v9fs 9p2000 file system support\\n");/i\
	pr_info("9P FileSystem: Starting v9fs initialization\\n");
    /err = v9fs_cache_register();/a\
	pr_info("9P FileSystem: Cache registration successful\\n");
    /err = v9fs_sysfs_init();/a\
	pr_info("9P FileSystem: Sysfs interface initialized\\n");
    /err = register_filesystem(&v9fs_fs_type);/a\
	pr_info("9P FileSystem: v9fs filesystem registered successfully\\n");\
	pr_info("9P FileSystem: All components loaded and ready\\n");
    s/pr_err("Failed to register v9fs for caching\\n");/pr_err("9P FileSystem: Failed to register v9fs for caching: %d\\n", err);/
    s/pr_err("Failed to register with sysfs\\n");/pr_err("9P FileSystem: Failed to register with sysfs: %d\\n", err);/
    s/pr_err("Failed to register filesystem\\n");/pr_err("9P FileSystem: Failed to register filesystem: %d\\n", err);/
}' common/fs/9p/v9fs.c

# 3. 修改Virtio传输层初始化日志
echo
echo "3. 修改Virtio传输层初始化 (net/9p/trans_virtio.c)..."
backup_file "common/net/9p/trans_virtio.c"

sed -i '/^static int __init p9_virtio_init(void)$/,/return rc;$/{
    /INIT_LIST_HEAD(&virtio_chan_list);/i\
	pr_info("9P Virtio: Initializing virtio transport\\n");
    /v9fs_register_trans(&p9_virtio_trans);/a\
	pr_info("9P Virtio: Transport module registered\\n");
    /rc = register_virtio_driver(&p9_virtio_drv);/a\
	if (rc) {\
		pr_err("9P Virtio: Failed to register virtio driver: %d\\n", rc);\
		v9fs_unregister_trans(&p9_virtio_trans);\
	} else {\
		pr_info("9P Virtio: Virtio driver registered successfully\\n");\
		pr_info("9P Virtio: Ready for virtio 9pfs connections\\n");\
	}
    /if (rc)/,/v9fs_unregister_trans(&p9_virtio_trans);/d
}' common/net/9p/trans_virtio.c

# 4. 添加会话初始化日志
echo
echo "4. 添加会话初始化日志..."
sed -i '/struct p9_fid \*v9fs_session_init/,/^{$/{
    /^{$/a\
	pr_info("9P Session: Initializing session for device: %s\\n", dev_name);
}' common/fs/9p/v9fs.c

sed -i '/v9ses->clnt = p9_client_create(dev_name, data);/,/goto err_names;$/{
    /goto err_names;$/i\
		pr_err("9P Session: Failed to create client for %s: %d\\n", dev_name, rc);
    /goto err_names;$/a\
	}\
	pr_info("9P Session: Client created successfully for %s\\n", dev_name);
}' common/fs/9p/v9fs.c

# 在函数返回前添加成功日志
sed -i '/return fid;$/i\
	pr_info("9P Session: Session initialized successfully, ready for operations\\n");' common/fs/9p/v9fs.c

echo
echo "=== 修改完成 ==="
echo "已为以下文件添加日志信息:"
echo "  - common/net/9p/mod.c"
echo "  - common/fs/9p/v9fs.c" 
echo "  - common/net/9p/trans_virtio.c"
echo
echo "备份文件已保存为 .backup 后缀"
echo
echo "现在重新编译内核，启动时应该能看到类似这样的日志:"
echo "  [    X.XXXXXX] 9P Protocol: Starting initialization"
echo "  [    X.XXXXXX] 9P Protocol: Core protocol support loaded successfully"
echo "  [    X.XXXXXX] 9P FileSystem: Starting v9fs initialization"
echo "  [    X.XXXXXX] 9P FileSystem: All components loaded and ready"
echo "  [    X.XXXXXX] 9P Virtio: Ready for virtio 9pfs connections"
echo
echo "使用 'dmesg | grep 9P' 查看启动日志"
