#include <stdio.h>
#include <sys/mount.h>
#include <errno.h>
#include <string.h>
#include <unistd.h>

int main() {
    const char *source = "hostshare";
    const char *target = "/data/local/tmp/test9p";
    const char *filesystemtype = "9p";
    const char *data = "trans=virtio,version=9p2000.L";
    
    printf("Testing 9pfs mount system call...\n");
    printf("Source: %s\n", source);
    printf("Target: %s\n", target);
    printf("FS Type: %s\n", filesystemtype);
    printf("Options: %s\n", data);
    
    // 尝试挂载
    int result = mount(source, target, filesystemtype, 0, data);
    
    if (result == 0) {
        printf("SUCCESS: 9pfs mount successful!\n");
        
        // 尝试卸载
        if (umount(target) == 0) {
            printf("SUCCESS: 9pfs umount successful!\n");
        } else {
            printf("WARNING: umount failed: %s\n", strerror(errno));
        }
    } else {
        printf("ERROR: 9pfs mount failed: %s (errno: %d)\n", strerror(errno), errno);
        
        // 打印一些有用的错误信息
        switch (errno) {
            case ENODEV:
                printf("HINT: 9pfs filesystem not supported by kernel\n");
                break;
            case ENOENT:
                printf("HINT: Mount point doesn't exist or virtio device not found\n");
                break;
            case EPERM:
                printf("HINT: Permission denied, try running as root\n");
                break;
            case EINVAL:
                printf("HINT: Invalid mount options or filesystem type\n");
                break;
        }
    }
    
    return result;
}
