#!/bin/bash

echo "=== 详细的9pfs mount测试 ==="

# 通过adb执行命令并捕获详细输出
test_mount() {
    local cmd="$1"
    local desc="$2"
    
    echo ">>> 测试: $desc"
    echo ">>> 命令: $cmd"
    
    # 执行命令并捕获stdout和stderr
    result=$(adb shell "$cmd" 2>&1)
    exit_code=$?
    
    echo ">>> 退出码: $exit_code"
    echo ">>> 输出:"
    echo "$result"
    echo ">>> 内核日志 (最后5行):"
    adb shell "dmesg | tail -5"
    echo "----------------------------------------"
    echo
}

# 准备测试环境
echo "准备测试环境..."
adb shell "mkdir -p /data/local/tmp/share"
adb shell "mkdir -p /sdcard/share"

# 检查基本信息
echo "1. 检查9pfs支持"
adb shell "cat /proc/filesystems | grep 9p"

echo "2. 检查virtio设备"
adb shell "ls -la /sys/bus/virtio/devices/ 2>/dev/null || echo 'No virtio devices found'"

echo "3. 检查当前挂载"
adb shell "mount | grep 9p || echo 'No 9p mounts found'"

echo
echo "开始mount测试..."

# 测试1：最简单的mount
test_mount "mount -t 9p -o trans=virtio hostshare /data/local/tmp/share" \
           "简单mount到/data/local/tmp/share"

# 测试2：如果上面失败，尝试不同的选项
test_mount "mount -t 9p -o trans=virtio,version=9p2000.L hostshare /data/local/tmp/share" \
           "指定9p2000.L版本"

# 测试3：添加调试选项
test_mount "mount -t 9p -o trans=virtio,debug=65535 hostshare /data/local/tmp/share" \
           "启用调试输出"

# 测试4：尝试不同的mount_tag
test_mount "mount -t 9p -o trans=virtio share /data/local/tmp/share" \
           "使用不同的mount_tag (share)"

# 测试5：检查是否成功挂载
echo ">>> 检查挂载结果"
adb shell "mount | grep 9p"
adb shell "ls -la /data/local/tmp/share/"

# 清理
echo ">>> 清理挂载点"
adb shell "umount /data/local/tmp/share 2>/dev/null || echo 'Nothing to unmount'"

echo "=== 测试完成 ==="
echo
echo "如果所有测试都失败，可能的原因："
echo "1. QEMU没有正确配置9pfs设备"
echo "2. mount_tag名称不匹配"
echo "3. virtio设备未正确初始化"
echo "4. SELinux权限问题"
