CONFIG_CPUFREQ_DUMMY=m
CONFIG_VIRTIO_VSOCKETS=m
CONFIG_GNSS_CMDLINE_SERIAL=m
CONFIG_VIRTIO_BALLOON=m
CONFIG_VIRTIO_BLK=m
CONFIG_VIRTIO_DMA_SHARED_BUFFER=m
CONFIG_VIRTIO_NET=m
CONFIG_VIRT_WIFI=m
CONFIG_HW_RANDOM_VIRTIO=m
CONFIG_SND_INTEL8X0=m
CONFIG_RTC_DRV_TEST=m
CONFIG_VIRTIO_PCI=m
CONFIG_VIRTIO_PMEM=m
CONFIG_VIRTIO_INPUT=m
CONFIG_VIRTIO_MMIO=m
CONFIG_VIRTIO_MMIO_CMDLINE_DEVICES=y
CONFIG_TEST_STACKINIT=m
CONFIG_TEST_MEMINIT=m
CONFIG_TCG_TPM=m
CONFIG_TCG_VTPM_PROXY=m
CONFIG_USBIP_CORE=m
CONFIG_USBIP_VHCI_HCD=m
CONFIG_VIRTIO_CONSOLE=m
CONFIG_ZRAM=m
CONFIG_ZSMALLOC=m
CONFIG_USB_PULSE8_CEC=m

CONFIG_SND_HDA_CODEC_REALTEK=m
CONFIG_SND_HDA_INTEL=m
CONFIG_BLK_DEV_MD=m
CONFIG_BT_HCIBTUSB=m
CONFIG_CFG80211=m
CONFIG_NL80211_TESTMODE=y
# CONFIG_CFG80211_DEFAULT_PS is not set
# CONFIG_CFG80211_CRDA_SUPPORT is not set
CONFIG_MAC80211=m

CONFIG_MAC80211_HWSIM=m
CONFIG_DMABUF_HEAPS_SYSTEM=m

CONFIG_CLK_VEXPRESS_OSC=m
CONFIG_MFD_VEXPRESS_SYSREG=m
CONFIG_MMC_ARMMMCI=m
CONFIG_MOUSE_PS2=m
CONFIG_SERIO_AMBAKMI=m
CONFIG_VEXPRESS_CONFIG=m

CONFIG_BT_HCIVHCI=m

CONFIG_CAN_VCAN=m
CONFIG_CAN_SLCAN=m
CONFIG_CAN_GS_USB=m
