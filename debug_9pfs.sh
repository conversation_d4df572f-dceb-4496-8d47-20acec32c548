#!/bin/bash

echo "=== 9pfs调试脚本 ==="
echo "检查Android系统中的9pfs支持状态"
echo

# 通过adb执行调试命令
debug_cmd() {
    echo ">>> $1"
    adb shell "$1" 2>/dev/null || echo "命令执行失败或无输出"
    echo
}

echo "1. 检查文件系统支持（最重要）"
debug_cmd "cat /proc/filesystems | grep 9p"
if adb shell "cat /proc/filesystems | grep -q 9p" 2>/dev/null; then
    echo "✓ 9pfs文件系统支持已启用"
else
    echo "✗ 9pfs文件系统支持未找到"
fi

echo "2. 检查内核模块（可能为空是正常的）"
debug_cmd "lsmod | grep 9p"
echo "注意：如果9pfs编译到内核中(=y)，lsmod不会显示，这是正常的"

echo "3. 检查virtio设备"
debug_cmd "ls -la /sys/bus/virtio/devices/"

echo "4. 检查virtio驱动"
debug_cmd "ls -la /sys/bus/virtio/drivers/"

echo "5. 检查9pfs相关内核日志"
debug_cmd "dmesg | grep -i '9p'"

echo "6. 检查virtio相关内核日志"
debug_cmd "dmesg | grep -i virtio"

echo "7. 检查mount命令版本和帮助"
debug_cmd "mount --help | head -10"

echo "8. 检查当前挂载点"
debug_cmd "mount | grep -E '(9p|virtio)'"

echo "9. 检查/proc/mounts"
debug_cmd "cat /proc/mounts | grep 9p"

echo "10. 检查SELinux状态"
debug_cmd "getenforce"

echo "11. 检查目标目录权限"
debug_cmd "ls -ld /sdcard/share"
debug_cmd "ls -ld /sdcard/"

echo "12. 尝试创建测试目录"
debug_cmd "mkdir -p /data/local/tmp/test9p"
debug_cmd "ls -ld /data/local/tmp/test9p"

echo "13. 检查内核配置（如果可用）"
debug_cmd "zcat /proc/config.gz | grep -i 9P"
if adb shell "zcat /proc/config.gz 2>/dev/null | grep -q 'CONFIG_.*9P.*=y'" 2>/dev/null; then
    echo "✓ 找到9pfs相关配置(=y)，说明编译到内核中"
elif adb shell "zcat /proc/config.gz 2>/dev/null | grep -q 'CONFIG_.*9P.*=m'" 2>/dev/null; then
    echo "! 找到9pfs相关配置(=m)，说明编译为模块，需要加载"
else
    echo "✗ 未找到9pfs配置或/proc/config.gz不可用"
fi

echo "14. 查找9pfs相关文件"
debug_cmd "find /system -name '*9p*' 2>/dev/null | head -5"
debug_cmd "find /vendor -name '*9p*' 2>/dev/null | head -5"

echo "=== 调试完成 ==="
echo
echo "建议的解决步骤："
echo "1. 如果没有看到9p文件系统支持，说明内核编译时未包含9pfs"
echo "2. 如果没有virtio设备，检查QEMU启动参数"
echo "3. 如果SELinux是enforcing，尝试setenforce 0"
echo "4. 尝试使用不同的挂载点，如/data/local/tmp/"
echo
echo "测试mount命令："
echo "adb shell mount -t 9p -o trans=virtio hostshare /data/local/tmp/test9p"
