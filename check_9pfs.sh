#!/bin/bash

# 9pfs功能验证脚本
# 用法: ./check_9pfs.sh [构建输出目录]

BUILD_DIR=${1:-"out/android12-5.10"}
DIST_DIR="$BUILD_DIR/dist"
COMMON_DIR="$BUILD_DIR/common"

echo "=== 9pfs功能验证脚本 ==="
echo "构建目录: $BUILD_DIR"
echo "分发目录: $DIST_DIR"
echo "通用目录: $COMMON_DIR"
echo

# 检查文件是否存在
check_file_exists() {
    local file=$1
    local desc=$2
    if [ -f "$file" ]; then
        echo "✅ $desc: $file ($(du -h "$file" | cut -f1))"
        return 0
    else
        echo "❌ $desc: $file (不存在)"
        return 1
    fi
}

# 1. 检查关键文件
echo "=== 1. 检查关键文件 ==="
check_file_exists "$DIST_DIR/vmlinux" "内核符号文件"
check_file_exists "$DIST_DIR/Image.lz4" "压缩内核镜像"
check_file_exists "$COMMON_DIR/.config" "内核配置文件"
echo

# 2. 检查配置文件中的9pfs设置
echo "=== 2. 检查内核配置中的9pfs设置 ==="
if [ -f "$COMMON_DIR/.config" ]; then
    echo "9pfs相关配置:"
    grep -E "CONFIG_(NET_)?9P|CONFIG_9P_FS" "$COMMON_DIR/.config" | while read line; do
        if [[ $line == *"=y"* ]]; then
            echo "  ✅ $line"
        elif [[ $line == *"=m"* ]]; then
            echo "  🔶 $line (模块)"
        elif [[ $line == *"is not set"* ]]; then
            echo "  ❌ $line"
        else
            echo "  ❓ $line"
        fi
    done
else
    echo "❌ 配置文件不存在"
fi
echo

# 3. 检查vmlinux中的9pfs符号
echo "=== 3. 检查vmlinux中的9pfs符号 ==="
if [ -f "$DIST_DIR/vmlinux" ]; then
    # 尝试不同的工具
    OBJDUMP_CMD=""
    for cmd in "${CROSS_COMPILE}objdump" "aarch64-linux-gnu-objdump" "objdump"; do
        if command -v "$cmd" >/dev/null 2>&1; then
            OBJDUMP_CMD="$cmd"
            break
        fi
    done
    
    if [ -n "$OBJDUMP_CMD" ]; then
        echo "使用工具: $OBJDUMP_CMD"
        echo "9pfs相关符号:"
        $OBJDUMP_CMD -t "$DIST_DIR/vmlinux" 2>/dev/null | grep -i 9p | head -10 | while read line; do
            echo "  ✅ $line"
        done
        
        # 检查特定的关键符号
        echo "关键9pfs符号检查:"
        for symbol in "virtio_9p" "v9fs_init" "p9_client" "net_9p"; do
            if $OBJDUMP_CMD -t "$DIST_DIR/vmlinux" 2>/dev/null | grep -q "$symbol"; then
                echo "  ✅ $symbol: 找到"
            else
                echo "  ❌ $symbol: 未找到"
            fi
        done
    else
        echo "❌ 未找到合适的objdump工具"
    fi
else
    echo "❌ vmlinux文件不存在"
fi
echo

# 4. 检查字符串
echo "=== 4. 检查内核镜像中的9pfs字符串 ==="
if [ -f "$DIST_DIR/vmlinux" ]; then
    echo "9pfs相关字符串:"
    strings "$DIST_DIR/vmlinux" 2>/dev/null | grep -i 9p | head -10 | while read line; do
        echo "  ✅ $line"
    done
else
    echo "❌ vmlinux文件不存在"
fi
echo

# 5. 检查Image.lz4
echo "=== 5. 检查压缩内核镜像 ==="
if [ -f "$DIST_DIR/Image.lz4" ]; then
    if command -v lz4 >/dev/null 2>&1; then
        echo "解压并检查Image.lz4..."
        TEMP_IMAGE="/tmp/kernel_image_$$"
        if lz4 -d "$DIST_DIR/Image.lz4" "$TEMP_IMAGE" >/dev/null 2>&1; then
            echo "9pfs字符串检查:"
            strings "$TEMP_IMAGE" 2>/dev/null | grep -i 9p | head -5 | while read line; do
                echo "  ✅ $line"
            done
            rm -f "$TEMP_IMAGE"
        else
            echo "❌ 无法解压Image.lz4"
        fi
    else
        echo "❌ lz4工具未安装"
    fi
else
    echo "❌ Image.lz4文件不存在"
fi
echo

# 6. 总结
echo "=== 6. 验证总结 ==="
if [ -f "$COMMON_DIR/.config" ] && grep -q "CONFIG_9P_FS=y" "$COMMON_DIR/.config"; then
    echo "✅ 9pfs功能已在配置中启用"
    
    if [ -f "$DIST_DIR/vmlinux" ]; then
        # 尝试检查符号
        OBJDUMP_CMD=""
        for cmd in "${CROSS_COMPILE}objdump" "aarch64-linux-gnu-objdump" "objdump"; do
            if command -v "$cmd" >/dev/null 2>&1; then
                OBJDUMP_CMD="$cmd"
                break
            fi
        done
        
        if [ -n "$OBJDUMP_CMD" ] && $OBJDUMP_CMD -t "$DIST_DIR/vmlinux" 2>/dev/null | grep -q -i 9p; then
            echo "✅ 内核镜像包含9pfs功能"
        else
            echo "❓ 无法确认内核镜像是否包含9pfs功能"
        fi
    else
        echo "❌ 无法检查内核镜像"
    fi
else
    echo "❌ 9pfs功能未在配置中启用或配置文件不存在"
fi

echo
echo "=== 验证完成 ==="
